import 'package:flutter/material.dart';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/ecommerce_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class EditableProductCard extends StatefulWidget {
  final Map<String, dynamic> productData;
  final VoidCallback onStockUpdated;

  const EditableProductCard({
    super.key,
    required this.productData,
    required this.onStockUpdated,
  });

  @override
  State<EditableProductCard> createState() => _EditableProductCardState();
}

class _EditableProductCardState extends State<EditableProductCard> {
  final EcommerceService _ecommerceService = EcommerceService();
  late TextEditingController _stockController;
  bool _isUpdating = false;

  @override
  void initState() {
    super.initState();
    _stockController = TextEditingController(
      text: (widget.productData['stock_count'] ?? 0).toString(),
    );
  }

  @override
  void dispose() {
    _stockController.dispose();
    super.dispose();
  }

  Future<void> _updateStock() async {
    final newQuantity = int.tryParse(_stockController.text);
    if (newQuantity == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a valid number')),
      );
      return;
    }

    setState(() => _isUpdating = true);

    try {
      final productId = widget.productData['_id']['\$oid'];
      await _ecommerceService.updateProductStock(productId, newQuantity);
      widget.onStockUpdated();
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('Stock updated!')));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error: ${e.toString()}')));
      }
    } finally {
      if (mounted) {
        setState(() => _isUpdating = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final String name =
        widget.productData['product_name']?.toString() ?? 'Unnamed Product';
    final double price =
        (widget.productData['price'] as num?)?.toDouble() ?? 0.0;
    final List<dynamic> mediaList =
        widget.productData['media'] as List<dynamic>? ?? [];
    final String? imagePath = mediaList.isNotEmpty
        ? mediaList.first?.toString()
        : null;

    return NeuCard(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          Row(
            children: [
              NeuCard(
                margin: EdgeInsets.zero,
                padding: EdgeInsets.zero,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(13),
                  child: SizedBox(
                    width: 80,
                    height: 80,
                    child: imagePath != null
                        ? FutureBuilder<String>(
                            future: ConfigService.instance.getBaseUrl(),
                            builder: (context, snapshot) {
                              if (snapshot.hasData) {
                                final fullUrl =
                                    '${snapshot.data}/${imagePath.replaceAll('\\', '/')}';
                                return Image.network(
                                  fullUrl,
                                  fit: BoxFit.cover,
                                );
                              }
                              return const Center(
                                child: CircularProgressIndicator(),
                              );
                            },
                          )
                        : Container(
                            color: Colors.grey.shade200,
                            child: const Icon(
                              Icons.image_not_supported,
                              color: Colors.grey,
                            ),
                          ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'GHS ${price.toStringAsFixed(2)}',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF6C5CE7),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              const Text(
                "Stock:",
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: NeuCard(
                  margin: EdgeInsets.zero,
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  child: TextField(
                    controller: _stockController,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(border: InputBorder.none),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              GestureDetector(
                onTap: _isUpdating ? null : _updateStock,
                child: NeuCard(
                  margin: EdgeInsets.zero,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  backgroundColor: const Color(0xFF00D2D3),
                  child: _isUpdating
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                      : const Text(
                          "Update",
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
