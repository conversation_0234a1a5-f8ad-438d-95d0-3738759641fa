from flask import request, jsonify, Blueprint
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId
import datetime

analytics_bp = Blueprint('analytics_bp', __name__)

@analytics_bp.route('/log-event', methods=['POST'])
@jwt_required()
def log_event():
    """
    Logs a user event, such as a search, view, or interaction.
    """
    db = analytics_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    data = request.get_json()

    event_type = data.get('event_type')
    if not event_type:
        return jsonify({"msg": "Event type is required"}), 400

    event_log = {
        "user_id": current_user_id,
        "event_type": event_type,
        "timestamp": datetime.datetime.now(datetime.timezone.utc),
        "metadata": data.get('metadata', {})
    }

    try:
        db.analytics.insert_one(event_log)
        return jsonify({"status": "ok"}), 200
    except Exception as e:
        # We don't want analytics failures to crash the user experience,
        # so we just log the error on the server.
        print(f"Failed to log analytics event: {e}")
        return jsonify({"status": "error"}), 500