import 'package:flutter/material.dart';
import 'package:wicker/screens/payment_webview_screen.dart';
import 'package:wicker/services/cart_service.dart';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/transaction_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class ProductCard extends StatefulWidget {
  final Map<String, dynamic> productData;

  const ProductCard({super.key, required this.productData});

  @override
  State<ProductCard> createState() => _ProductCardState();
}

class _ProductCardState extends State<ProductCard> {
  final CartService _cartService = CartService();
  bool _isAddingToCart = false;

  void _addToCart() async {
    if (_isAddingToCart) return;

    setState(() => _isAddingToCart = true);

    try {
      final productId = widget.productData['_id']['\$oid'];
      await _cartService.addToCart(productId);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '${widget.productData['product_name']} added to cart.',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isAddingToCart = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final String name =
        widget.productData['product_name']?.toString() ?? 'Unnamed Product';
    final String description =
        widget.productData['description']?.toString() ??
        'No description available.';
    final double price =
        (widget.productData['price'] as num?)?.toDouble() ?? 0.0;
    final String type =
        widget.productData['product_type']?.toString() ?? 'physical';
    final bool inStock = (widget.productData['stock_count'] as int? ?? 0) > 0;

    final List<dynamic> mediaList =
        widget.productData['media'] as List<dynamic>? ?? [];
    final String? imagePath = mediaList.isNotEmpty
        ? mediaList.first?.toString()
        : null;

    Color typeColor;
    IconData typeIcon;

    switch (type) {
      case 'digital':
        typeColor = const Color(0xFFFFE66D); // Yellow
        typeIcon = Icons.cloud_download;
        break;
      case 'service':
        typeColor = const Color(0xFFFF6B6B); // Coral
        typeIcon = Icons.handyman;
        break;
      case 'physical':
      default:
        typeColor = const Color(0xFF4ECDC4); // Teal
        typeIcon = Icons.shopping_bag;
        break;
    }

    return NeuCard(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              NeuCard(
                margin: EdgeInsets.zero,
                padding: EdgeInsets.zero,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(13),
                  child: SizedBox(
                    width: 80,
                    height: 80,
                    child: imagePath != null
                        ? FutureBuilder<String>(
                            future: ConfigService.instance.getBaseUrl(),
                            builder: (context, snapshot) {
                              if (snapshot.hasData) {
                                final fullUrl =
                                    '${snapshot.data}/${imagePath.replaceAll('\\', '/')}';
                                return Image.network(
                                  fullUrl,
                                  fit: BoxFit.cover,
                                );
                              }
                              return const Center(
                                child: CircularProgressIndicator(),
                              );
                            },
                          )
                        : Container(
                            color: Colors.grey.shade200,
                            child: const Icon(
                              Icons.image_not_supported,
                              color: Colors.grey,
                            ),
                          ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: TextStyle(fontSize: 14, color: Colors.grey[700]),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'GHS ${price.toStringAsFixed(2)}',
                    style: const TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF6C5CE7),
                    ),
                  ),
                  NeuChip(
                    label: type.toUpperCase(),
                    icon: typeIcon,
                    backgroundColor: typeColor,
                    textColor: typeColor == const Color(0xFFFFE66D)
                        ? Colors.black
                        : Colors.white,
                  ),
                ],
              ),
              GestureDetector(
                onTap: inStock && !_isAddingToCart ? _addToCart : null,
                child: NeuCard(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 10,
                  ),
                  margin: EdgeInsets.zero,
                  shadowOffset: 4.0,
                  borderWidth: 2.0,
                  borderRadius: 20,
                  backgroundColor: inStock
                      ? const Color(0xFF00D2D3)
                      : Colors.grey.shade300,
                  child: _isAddingToCart
                      ? const SizedBox(
                          height: 15,
                          width: 15,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        )
                      : Text(
                          inStock ? 'ADD TO CART' : 'SOLD OUT',
                          style: TextStyle(
                            color: inStock ? Colors.white : Colors.grey[700],
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
