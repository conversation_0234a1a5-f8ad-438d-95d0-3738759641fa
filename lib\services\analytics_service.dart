import 'dart:convert';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/places_service.dart'; // For WickerHttpClient

class AnalyticsService {
  final WickerHttpClient _client = WickerHttpClient();
  final ConfigService _config = ConfigService.instance;

  Future<void> logEvent(String eventType, Map<String, dynamic> metadata) async {
    // We use a try-catch block here because analytics should not crash the app.
    // If the event fails to log, the user experience should not be interrupted.
    try {
      final baseUrl = await _config.getBaseUrl();
      await _client.post(
        Uri.parse('$baseUrl/api/analytics/log-event'),
        body: jsonEncode({'event_type': eventType, 'metadata': metadata}),
      );
      print('Logged event: $eventType');
    } catch (e) {
      print('Failed to log analytics event: $e');
    }
  }
}
