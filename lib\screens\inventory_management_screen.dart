// import 'package:flutter/material.dart';
// import 'package:wicker/screens/add_product_screen.dart';
// import 'package:wicker/services/ecommerce_service.dart';
// import 'package:wicker/widgets/editable_product_card.dart';
// import 'package:wicker/widgets/neubrutalist_widgets.dart';

// class InventoryManagementScreen extends StatefulWidget {
//   final Map<String, dynamic> businessData;
//   const InventoryManagementScreen({super.key, required this.businessData});

//   @override
//   State<InventoryManagementScreen> createState() =>
//       _InventoryManagementScreenState();
// }

// class _InventoryManagementScreenState extends State<InventoryManagementScreen> {
//   final EcommerceService _ecommerceService = EcommerceService();
//   late Future<List<Map<String, dynamic>>> _productsFuture;

//   @override
//   void initState() {
//     super.initState();
//     _fetchProducts();
//   }

//   void _fetchProducts() {
//     final businessId = widget.businessData['_id']['\$oid'];
//     setState(() {
//       _productsFuture = _ecommerceService.getBusinessProducts(businessId);
//     });
//   }

//   @override
//   Widget build(BuildContext context) {
//     final businessId = widget.businessData['_id']['\$oid'];

//     return Scaffold(
//       backgroundColor: const Color(0xFFFEF7F0),
//       appBar: PreferredSize(
//         preferredSize: const Size.fromHeight(60.0),
//         child: AppBar(
//           backgroundColor: Colors.white,
//           elevation: 0,
//           leading: IconButton(
//             icon: const Icon(Icons.arrow_back, color: Colors.black),
//             onPressed: () => Navigator.of(context).pop(),
//           ),
//           title: Text(
//             widget.businessData['business_name'] ?? 'My Inventory',
//             style: const TextStyle(
//               color: Colors.black,
//               fontWeight: FontWeight.bold,
//             ),
//           ),
//           bottom: PreferredSize(
//             preferredSize: const Size.fromHeight(4.0),
//             child: Container(color: Colors.black, height: 3.0),
//           ),
//         ),
//       ),
//       body: FutureBuilder<List<Map<String, dynamic>>>(
//         future: _productsFuture,
//         builder: (context, snapshot) {
//           if (snapshot.connectionState == ConnectionState.waiting) {
//             return const Center(child: CircularProgressIndicator());
//           }
//           if (snapshot.hasError) {
//             return Center(child: Text('Error: ${snapshot.error}'));
//           }
//           if (!snapshot.hasData || snapshot.data!.isEmpty) {
//             return Center(
//               child: NeuCard(
//                 backgroundColor: const Color(0xFFFFE66D),
//                 child: Padding(
//                   padding: const EdgeInsets.all(16.0),
//                   child: Column(
//                     mainAxisSize: MainAxisSize.min,
//                     children: [
//                       Icon(
//                         Icons.inventory_2_outlined,
//                         size: 48,
//                         color: Colors.grey[800],
//                       ),
//                       const SizedBox(height: 16),
//                       const Text(
//                         'NO PRODUCTS YET',
//                         style: TextStyle(
//                           fontSize: 18,
//                           fontWeight: FontWeight.bold,
//                         ),
//                       ),
//                       const SizedBox(height: 8),
//                       Text(
//                         'Tap the "+" button to add your first product.',
//                         style: TextStyle(color: Colors.grey[800]),
//                         textAlign: TextAlign.center,
//                       ),
//                     ],
//                   ),
//                 ),
//               ),
//             );
//           }

//           final products = snapshot.data!;
//           return RefreshIndicator(
//             onRefresh: () async => _fetchProducts(),
//             child: ListView.builder(
//               padding: const EdgeInsets.symmetric(vertical: 8),
//               itemCount: products.length,
//               itemBuilder: (context, index) {
//                 return EditableProductCard(
//                   productData: products[index],
//                   onStockUpdated: _fetchProducts,
//                 );
//               },
//             ),
//           );
//         },
//       ),
//       floatingActionButton: FloatingActionButton.extended(
//         onPressed: () async {
//           final result = await Navigator.push<bool>(
//             context,
//             MaterialPageRoute(
//               builder: (context) => AddProductScreen(businessId: businessId),
//             ),
//           );
//           if (result == true) {
//             _fetchProducts();
//           }
//         },
//         label: const Text('Add Product'),
//         icon: const Icon(Icons.add),
//         backgroundColor: const Color(0xFF00D2D3),
//       ),
//     );
//   }
// }

import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:flutter/material.dart';
import 'package:wicker/screens/add_product_screen.dart';
import 'package:wicker/screens/sales_history_screen.dart';
import 'package:wicker/services/ecommerce_service.dart';
import 'package:wicker/widgets/editable_product_card.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class InventoryManagementScreen extends StatefulWidget {
  final Map<String, dynamic> businessData;
  const InventoryManagementScreen({super.key, required this.businessData});

  @override
  State<InventoryManagementScreen> createState() =>
      _InventoryManagementScreenState();
}

class _InventoryManagementScreenState extends State<InventoryManagementScreen> {
  final EcommerceService _ecommerceService = EcommerceService();
  late Future<List<Map<String, dynamic>>> _productsFuture;

  @override
  void initState() {
    super.initState();
    _fetchProducts();
  }

  void _fetchProducts() {
    final businessId = widget.businessData['_id']['\$oid'];
    setState(() {
      _productsFuture = _ecommerceService.getBusinessProducts(businessId);
    });
  }

  @override
  Widget build(BuildContext context) {
    final businessId = widget.businessData['_id']['\$oid'];

    return Scaffold(
      backgroundColor: const Color(0xFFFEF7F0),
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(60.0),
        child: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.black),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Text(
            widget.businessData['business_name'] ?? 'My Inventory',
            style: const TextStyle(
              color: Colors.black,
              fontWeight: FontWeight.bold,
            ),
          ),
          actions: [
            IconButton(
              icon: const Icon(EvaIcons.barChart2Outline, color: Colors.black),
              tooltip: 'Sales History',
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SalesHistoryScreen(),
                  ),
                );
              },
            ),
          ],
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(4.0),
            child: Container(color: Colors.black, height: 3.0),
          ),
        ),
      ),
      body: FutureBuilder<List<Map<String, dynamic>>>(
        future: _productsFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          }
          if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return Center(
              child: NeuCard(
                backgroundColor: const Color(0xFFFFE66D),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.inventory_2_outlined,
                        size: 48,
                        color: Colors.grey[800],
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'NO PRODUCTS YET',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Tap the "+" button to add your first product.',
                        style: TextStyle(color: Colors.grey[800]),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            );
          }

          final products = snapshot.data!;
          return RefreshIndicator(
            onRefresh: () async => _fetchProducts(),
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(vertical: 8),
              itemCount: products.length,
              itemBuilder: (context, index) {
                return EditableProductCard(
                  productData: products[index],
                  onStockUpdated: _fetchProducts,
                );
              },
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () async {
          final result = await Navigator.push<bool>(
            context,
            MaterialPageRoute(
              builder: (context) => AddProductScreen(businessId: businessId),
            ),
          );
          if (result == true) {
            _fetchProducts();
          }
        },
        label: const Text('Add Product'),
        icon: const Icon(Icons.add),
        backgroundColor: const Color(0xFF00D2D3),
      ),
    );
  }
}
