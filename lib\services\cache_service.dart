import 'dart:convert';
import 'package:hive/hive.dart';

class CacheService {
  static final CacheService _instance = CacheService._internal();
  factory CacheService() => _instance;
  CacheService._internal();

  final Box _box = Hive.box('api_cache');

  // Retrieves and decodes cached data if it exists and hasn't expired
  Future<dynamic> get(String key) async {
    final cachedItem = _box.get(key);
    if (cachedItem == null || cachedItem is! String) {
      return null;
    }

    try {
      final decoded = jsonDecode(cachedItem) as Map<String, dynamic>;
      final expiry = DateTime.parse(decoded['expiry'] as String);

      if (expiry.isAfter(DateTime.now())) {
        // Cache is valid
        print('CACHE HIT for key: $key');
        return decoded['data'];
      } else {
        // Cache has expired
        print('CACHE EXPIRED for key: $key');
        await _box.delete(key);
        return null;
      }
    } catch (e) {
      print('Cache read error: $e');
      return null;
    }
  }

  // Encodes and saves data to the cache with an expiry date
  Future<void> set(String key, dynamic data, Duration duration) async {
    try {
      final expiry = DateTime.now().add(duration);
      final itemToCache = {'data': data, 'expiry': expiry.toIso8601String()};
      await _box.put(key, jsonEncode(itemToCache));
      print('CACHED data for key: $key');
    } catch (e) {
      print('Cache write error: $e');
    }
  }
}
