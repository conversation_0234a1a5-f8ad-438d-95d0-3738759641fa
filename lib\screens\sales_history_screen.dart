import 'dart:async';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:flutter/material.dart';
import 'package:wicker/services/transaction_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';
import 'package:wicker/widgets/sales_card.dart';


class SalesHistoryScreen extends StatefulWidget {
  const SalesHistoryScreen({super.key});

  @override
  State<SalesHistoryScreen> createState() => _SalesHistoryScreenState();
}

class _SalesHistoryScreenState extends State<SalesHistoryScreen> {
  final TransactionService _transactionService = TransactionService();
  final TextEditingController _searchController = TextEditingController();
  Timer? _debounce;

  Future<Map<String, dynamic>>? _salesFuture;
  String _searchQuery = '';
  String _activeStatusFilter = 'all';

  @override
  void initState() {
    super.initState();
    _fetchSales();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  void _fetchSales() {
    setState(() {
      _salesFuture = _transactionService.getMySales(
        searchQuery: _searchQuery,
        status: _activeStatusFilter,
      );
    });
  }

  void _onSearchChanged() {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      if (_searchQuery != _searchController.text) {
        setState(() {
          _searchQuery = _searchController.text;
        });
        _fetchSales();
      }
    });
  }

  void _changeStatusFilter(String newStatus) {
    setState(() {
      _activeStatusFilter = newStatus;
    });
    _fetchSales();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFEF7F0),
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(60.0),
        child: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.black),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: const Text(
            'Sales History',
            style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
          ),
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(4.0),
            child: Container(color: Colors.black, height: 3.0),
          ),
        ),
      ),
      body: FutureBuilder<Map<String, dynamic>>(
        future: _salesFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          }
          if (!snapshot.hasData) {
            return const Center(child: Text('No data available.'));
          }

          final salesData = List<Map<String, dynamic>>.from(
            snapshot.data!['sales_data'] ?? [],
          );
          final summaryStats = List<Map<String, dynamic>>.from(
            snapshot.data!['summary_stats'] ?? [],
          );

          return Column(
            children: [
              _buildControlsAndSummary(summaryStats),
              Expanded(
                child: RefreshIndicator(
                  onRefresh: () async => _fetchSales(),
                  child: salesData.isEmpty
                      ? Center(
                          child: Text(
                            _searchQuery.isEmpty
                                ? 'You have no sales for this status.'
                                : 'No sales found for "$_searchQuery".',
                            style: const TextStyle(
                              fontSize: 16,
                              color: Colors.grey,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.only(top: 8),
                          itemCount: salesData.length,
                          itemBuilder: (context, index) {
                           
                            return SalesCard(
                              salesData: salesData[index],
                              onUpdate:
                                  _refreshSales, // <-- Pass the callback here
                            );
                          },
                        ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildControlsAndSummary(List<Map<String, dynamic>> summaryStats) {
    double totalPaid = 0;
    double totalPending = 0;

    for (var stat in summaryStats) {
      if (stat['_id'] == 'paid') {
        totalPaid = (stat['total_sales'] as num?)?.toDouble() ?? 0.0;
      } else if (stat['_id'] == 'pending') {
        totalPending = (stat['total_sales'] as num?)?.toDouble() ?? 0.0;
      }
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: NeuCard(
                  margin: EdgeInsets.zero,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: TextField(
                    controller: _searchController,
                    decoration: const InputDecoration(
                      hintText: 'Search by product name...',
                      border: InputBorder.none,
                      icon: Icon(EvaIcons.search),
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildFilterChip('All', 'all'),
              _buildFilterChip('Paid', 'paid'),
              _buildFilterChip('Pending', 'pending'),
            ],
          ),
          const SizedBox(height: 16),
          NeuCard(
            margin: EdgeInsets.zero,
            backgroundColor: const Color(0xFFF9F9F9),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildSummaryStat("Paid", totalPaid, Colors.green),
                _buildSummaryStat("Pending", totalPending, Colors.orange),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String status) {
    final bool isSelected = _activeStatusFilter == status;
    return GestureDetector(
      onTap: () => _changeStatusFilter(status),
      child: NeuCard(
        margin: const EdgeInsets.symmetric(horizontal: 4),
        backgroundColor: isSelected ? const Color(0xFF6C5CE7) : Colors.white,
        child: Text(
          label,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: isSelected ? Colors.white : Colors.black,
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryStat(String label, double amount, Color color) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        children: [
          Text(
            label,
            style: TextStyle(
              color: Colors.grey[700],
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'GHS ${amount.toStringAsFixed(2)}',
            style: TextStyle(
              color: color,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
