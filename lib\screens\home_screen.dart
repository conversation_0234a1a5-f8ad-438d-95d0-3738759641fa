import 'dart:async';
import 'package:flutter/material.dart';
import 'package:wicker/widgets/home_search_bar.dart';
import 'package:wicker/widgets/post_card.dart';
import 'package:wicker/services/post_service.dart';

class HomeScreen extends StatefulWidget {
  final VoidCallback onSearchTap;
  const HomeScreen({super.key, required this.onSearchTap});

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final PostService _postService = PostService();
  late Future<List<Map<String, dynamic>>> _postsFuture;

  @override
  void initState() {
    super.initState();
    _postsFuture = _postService.getPosts();
    // Polling can be added back if desired
  }

  Future<void> _refreshPosts() async {
    setState(() {
      _postsFuture = _postService.getPosts();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          Container(
            padding: const EdgeInsets.only(top: 40.0, bottom: 8.0),
            child: HomeSearchBar(onTap: widget.onSearchTap),
          ),
          Expanded(child: _buildBodyContent()),
        ],
      ),
    );
  }

  Widget _buildBodyContent() {
    return FutureBuilder<List<Map<String, dynamic>>>(
      future: _postsFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        if (snapshot.hasError || !snapshot.hasData || snapshot.data!.isEmpty) {
          return const Center(child: Text('No posts found. Create one!'));
        }

        final allPosts = snapshot.data!;

        return RefreshIndicator(
          onRefresh: _refreshPosts,
          child: ListView.builder(
            padding: EdgeInsets.zero,
            itemCount: allPosts.length,
            itemBuilder: (context, index) {
              final post = allPosts[index];
              // Simply pass the raw data. The PostCard handles the rest.
              return PostCard(postData: post);
            },
          ),
        );
      },
    );
  }
}
