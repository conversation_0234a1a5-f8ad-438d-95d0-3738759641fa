import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:flutter/material.dart';
import 'package:wicker/screens/create_business_screen.dart';
import 'package:wicker/screens/edit_profile_screen.dart';
import 'package:wicker/screens/inventory_management_screen.dart';
import 'package:wicker/screens/orders_screen.dart';
import 'package:wicker/services/auth_service.dart';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/ecommerce_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';
import 'package:wicker/widgets/user_contributions_tab.dart';
import 'package:wicker/widgets/user_playlists_tab.dart';
import 'package:wicker/services/user_service.dart';

class HubScreen extends StatefulWidget {
  final String? userId;
  const HubScreen({super.key, this.userId});

  @override
  _HubScreenState createState() => _HubScreenState();
}

class _HubScreenState extends State<HubScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  final UserService _userService = UserService();
  final AuthService _authService = AuthService();
  final EcommerceService _ecommerceService = EcommerceService();
  final ConfigService _configService = ConfigService.instance;

  // --- REFACTORED: Initialize with a placeholder Future ---
  late Future<Map<String, dynamic>> _profileFuture = Future.value({});
  String? _currentUserId;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    // --- REFACTORED: Call the initialization method directly ---
    _initializeProfile();
  }

  // --- REFACTORED: This method is now async and handles assigning the future ---
  void _initializeProfile() async {
    final token = await _authService.getAccessToken();
    if (token != null) {
      _currentUserId = _authService.getUserIdFromToken(token);
    }
    // The key change: setState is called here after the async work is done,
    // which triggers a rebuild with the correct future.
    setState(() {
      _profileFuture = _fetchProfileData();
    });
  }

  // --- NEW: Helper function to select the correct profile fetch method ---
  Future<Map<String, dynamic>> _fetchProfileData() {
    if (widget.userId != null && widget.userId != _currentUserId) {
      return _userService.getUserProfile(widget.userId!);
    } else {
      return _userService.getProfileHeader();
    }
  }

  void _refreshProfile() {
    setState(() {
      _profileFuture = _fetchProfileData();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFEF7F0),
      body: FutureBuilder<Map<String, dynamic>>(
        future: _profileFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError ||
              !snapshot.hasData ||
              snapshot.data!.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text('Could not load profile.'),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: _refreshProfile,
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          final profileData = snapshot.data!;
          final isOwnProfile =
              widget.userId == null || widget.userId == _currentUserId;

          return NestedScrollView(
            headerSliverBuilder: (context, innerBoxIsScrolled) {
              return [
                SliverAppBar(
                  title: Text(
                    profileData['username'] ?? 'Profile',
                    style: const TextStyle(
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  backgroundColor: Colors.white,
                  elevation: 0,
                  pinned: true,
                  actions: [
                    if (isOwnProfile)
                      IconButton(
                        icon: const Icon(
                          EvaIcons.settings2Outline,
                          color: Colors.black,
                        ),
                        onPressed: () {
                          /* Nav to settings */
                        },
                      ),
                  ],
                  bottom: PreferredSize(
                    preferredSize: const Size.fromHeight(4.0),
                    child: Container(color: Colors.black, height: 3.0),
                  ),
                ),
                SliverToBoxAdapter(child: _buildProfileHeader(profileData)),
              ];
            },
            body: Column(
              children: [
                NeuCard(
                  margin: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                  padding: EdgeInsets.zero,
                  child: TabBar(
                    controller: _tabController,
                    indicator: const BoxDecoration(
                      color: Color(0xFFFFE66D),
                      border: Border(
                        bottom: BorderSide(color: Colors.black, width: 3.0),
                      ),
                    ),
                    indicatorSize: TabBarIndicatorSize.tab,
                    labelColor: Colors.black,
                    unselectedLabelColor: Colors.grey.shade700,
                    labelStyle: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                    tabs: const [
                      Tab(icon: Icon(EvaIcons.grid), text: 'Contributions'),
                      Tab(icon: Icon(EvaIcons.bookmark), text: 'Lists'),
                    ],
                  ),
                ),
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: const [
                      UserContributionsTab(),
                      UserPlaylistsTab(),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildProfileHeader(Map<String, dynamic> profile) {
    return FutureBuilder<String>(
      future: _configService.getBaseUrl(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const SizedBox(
            height: 250,
            child: Center(child: CircularProgressIndicator()),
          );
        }

        final baseUrl = snapshot.data!;
        String profilePicPath = profile['profile_pic_url']?.toString() ?? '';
        String profilePicUrl = profilePicPath.isNotEmpty
            ? '$baseUrl/${profilePicPath.replaceAll('\\', '/')}'
            : 'https://i.pravatar.cc/150?u=${profile['_id']?['\$oid']}';

        final isOwnProfile =
            widget.userId == null || widget.userId == _currentUserId;

        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  NeuCard(
                    margin: EdgeInsets.zero,
                    padding: const EdgeInsets.all(4),
                    child: CircleAvatar(
                      radius: 60,
                      backgroundImage: NetworkImage(profilePicUrl),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: NeuCard(
                      margin: EdgeInsets.zero,
                      padding: const EdgeInsets.all(2),
                      child: GridView.count(
                        crossAxisCount: 2,
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        mainAxisSpacing: 4,
                        crossAxisSpacing: 4,
                        childAspectRatio: 1.4,
                        children: [
                          if (isOwnProfile)
                            _buildStatQuad(
                              "Balance",
                              'GHS ${(profile['balance'] as num?)?.toDouble() ?? 0.0}',
                              EvaIcons.creditCardOutline,
                            ),
                          _buildStatQuad(
                            "Points",
                            (profile['points'] ?? 0).toString(),
                            EvaIcons.starOutline,
                          ),
                          _buildStatQuad(
                            "Followers",
                            (profile['followers_count'] ?? 0).toString(),
                            EvaIcons.peopleOutline,
                          ),
                          _buildStatQuad(
                            "Following",
                            (profile['following_count'] ?? 0).toString(),
                            EvaIcons.personDoneOutline,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                profile['username'] ?? '',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                profile['bio'] ?? 'No bio yet.',
                style: TextStyle(color: Colors.grey[800], fontSize: 16),
              ),
              const SizedBox(height: 16),
              _buildActionButtons(profile),
              const SizedBox(height: 8),
              if (isOwnProfile)
                GestureDetector(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const OrdersScreen(),
                      ),
                    );
                  },
                  child: const NeuCard(
                    margin: EdgeInsets.zero,
                    padding: EdgeInsets.all(12),
                    child: Row(
                      children: [
                        Icon(
                          EvaIcons.shoppingBagOutline,
                          color: Color(0xFF6C5CE7),
                        ),
                        SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            "My Orders",
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ),
                        Icon(
                          EvaIcons.arrowIosForwardOutline,
                          color: Colors.grey,
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatQuad(String label, String value, IconData icon) {
    return NeuCard(
      margin: EdgeInsets.zero,
      padding: const EdgeInsets.all(4),
      backgroundColor: Colors.white,
      shadowOffset: 0,
      borderWidth: 1.5,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 18, color: Colors.grey[800]),
          const SizedBox(height: 0.5),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
          ),
          Text(label, style: TextStyle(color: Colors.grey[600], fontSize: 10)),
        ],
      ),
    );
  }

  Widget _buildActionButtons(Map<String, dynamic> profileData) {
    final isOwnProfile =
        widget.userId == null || widget.userId == _currentUserId;

    if (isOwnProfile) {
      return Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () async {
                final result = await Navigator.push<bool>(
                  context,
                  MaterialPageRoute(
                    builder: (context) =>
                        EditProfileScreen(profileData: profileData),
                  ),
                );
                if (result == true) {
                  _refreshProfile();
                }
              },
              child: const NeuCard(
                margin: EdgeInsets.zero,
                padding: EdgeInsets.symmetric(vertical: 12),
                child: Center(
                  child: Text(
                    "Edit Profile",
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: GestureDetector(
              onTap: () async {
                final business = await _ecommerceService.getMyBusiness();
                if (mounted) {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => business != null
                          ? InventoryManagementScreen(businessData: business)
                          : const CreateBusinessScreen(),
                    ),
                  );
                }
              },
              child: const NeuCard(
                margin: EdgeInsets.zero,
                padding: EdgeInsets.symmetric(vertical: 12),
                backgroundColor: Color(0xFF00D2D3),
                child: Center(
                  child: Text(
                    "My Business",
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      );
    } else {
      // Buttons for viewing someone else's profile
      return Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () {
                /* TODO: Implement Follow/Unfollow logic */
              },
              child: const NeuCard(
                margin: EdgeInsets.zero,
                padding: EdgeInsets.symmetric(vertical: 12),
                backgroundColor: Color(0xFF6C5CE7),
                child: Center(
                  child: Text(
                    "Follow",
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: GestureDetector(
              onTap: () {
                /* TODO: Implement messaging */
              },
              child: const NeuCard(
                margin: EdgeInsets.zero,
                padding: EdgeInsets.symmetric(vertical: 12),
                child: Center(
                  child: Text(
                    "Message",
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ),
          ),
        ],
      );
    }
  }

  // This helper is for the old layout and can be removed or repurposed if not needed elsewhere.
  Widget _buildStatColumn(String label, String count) {
    return Expanded(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            count,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
          ),
          const SizedBox(height: 2),
          Text(label, style: const TextStyle(color: Colors.grey, fontSize: 12)),
        ],
      ),
    );
  }
}
