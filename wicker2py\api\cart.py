from flask import request, jsonify, Blueprint, Response
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId, json_util
import datetime

cart_bp = Blueprint('cart_bp', __name__)

@cart_bp.route('/', methods=['GET'])
@jwt_required()
def get_cart():
    """
    Retrieves the current user's cart and populates it with full product details.
    """
    db = cart_bp.db
    current_user_id = ObjectId(get_jwt_identity())

    pipeline = [
        {'$match': {'user_id': current_user_id}},
        {'$unwind': '$items'},
        {
            '$lookup': {
                'from': 'products',
                'localField': 'items.product_id',
                'foreignField': '_id',
                'as': 'product_details'
            }
        },
        {'$unwind': '$product_details'},
        {
            '$group': {
                '_id': '$_id',
                'user_id': {'$first': '$user_id'},
                'items': {
                    '$push': {
                        'product_id': '$items.product_id',
                        'quantity': '$items.quantity',
                        'product_details': '$product_details'
                    }
                },
                'created_at': {'$first': '$created_at'},
                'updated_at': {'$first': '$updated_at'}
            }
        }
    ]

    cart_data = list(db.carts.aggregate(pipeline))
    
    if not cart_data:
        return jsonify({"msg": "Cart is empty", "items": []}), 200

    return Response(json_util.dumps(cart_data[0]), mimetype='application/json'), 200

@cart_bp.route('/add', methods=['POST'])
@jwt_required()
def add_to_cart():
    """
    Adds a product to the cart. If the product is already in the cart,
    it increments the quantity.
    """
    db = cart_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    data = request.get_json()
    product_id = ObjectId(data.get('product_id'))
    quantity = data.get('quantity', 1)

    # Check product stock
    product = db.products.find_one({"_id": product_id})
    if not product or product.get('stock_count', 0) < quantity:
        return jsonify({"msg": "Product not found or insufficient stock"}), 404

    # Find the user's cart, or create one if it doesn't exist
    user_cart = db.carts.find_one({'user_id': current_user_id})

    if user_cart:
        # Check if the item already exists in the cart
        item_exists = db.carts.find_one({
            '_id': user_cart['_id'],
            'items.product_id': product_id
        })
        
        if item_exists:
            # If item exists, increment its quantity
            db.carts.update_one(
                {'_id': user_cart['_id'], 'items.product_id': product_id},
                {'$inc': {'items.$.quantity': quantity}}
            )
        else:
            # If item does not exist, add it to the items array
            new_item = {'product_id': product_id, 'quantity': quantity}
            db.carts.update_one(
                {'_id': user_cart['_id']},
                {'$push': {'items': new_item}}
            )
    else:
        # If the user has no cart, create a new one
        new_cart = {
            'user_id': current_user_id,
            'items': [{'product_id': product_id, 'quantity': quantity}],
            'created_at': datetime.datetime.now(datetime.timezone.utc),
            'updated_at': datetime.datetime.now(datetime.timezone.utc)
        }
        db.carts.insert_one(new_cart)
        
    return jsonify({"msg": "Item added to cart"}), 200

@cart_bp.route('/update/<product_id>', methods=['PUT'])
@jwt_required()
def update_cart_item(product_id):
    """
    Updates the quantity of a specific item in the cart.
    """
    db = cart_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    data = request.get_json()
    quantity = data.get('quantity')

    if quantity is None or not isinstance(quantity, int) or quantity <= 0:
        return jsonify({"msg": "A valid, positive integer 'quantity' is required"}), 400

    result = db.carts.update_one(
        {'user_id': current_user_id, 'items.product_id': ObjectId(product_id)},
        {'$set': {'items.$.quantity': quantity}}
    )

    if result.matched_count == 0:
        return jsonify({"msg": "Item not found in cart"}), 404
        
    return jsonify({"msg": "Cart updated"}), 200

@cart_bp.route('/remove/<product_id>', methods=['DELETE'])
@jwt_required()
def remove_from_cart(product_id):
    """
    Removes an item completely from the cart.
    """
    db = cart_bp.db
    current_user_id = ObjectId(get_jwt_identity())

    result = db.carts.update_one(
        {'user_id': current_user_id},
        {'$pull': {'items': {'product_id': ObjectId(product_id)}}}
    )

    if result.modified_count == 0:
        return jsonify({"msg": "Item not found in cart"}), 404

    return jsonify({"msg": "Item removed from cart"}), 200


# Add this endpoint to your cart.py file

@cart_bp.route('/clear', methods=['DELETE'])
@jwt_required()
def clear_cart():
    """
    Clears all items from the user's cart.
    """
    db = cart_bp.db
    current_user_id = ObjectId(get_jwt_identity())

    result = db.carts.delete_one({'user_id': current_user_id})

    if result.deleted_count == 0:
        return jsonify({"msg": "Cart was already empty"}), 200
        
    return jsonify({"msg": "Cart cleared successfully"}), 200