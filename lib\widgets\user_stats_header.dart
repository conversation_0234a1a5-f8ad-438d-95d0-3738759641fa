import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:flutter/material.dart';
import 'package:wicker/services/user_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class UserStatsHeader extends StatefulWidget {
  const UserStatsHeader({super.key});

  @override
  State<UserStatsHeader> createState() => _UserStatsHeaderState();
}

class _UserStatsHeaderState extends State<UserStatsHeader> {
  final UserService _userService = UserService();
  Future<Map<String, dynamic>>? _statsFuture;

  @override
  void initState() {
    super.initState();
    _statsFuture = _userService.getUserStats();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Map<String, dynamic>>(
      future: _statsFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        if (snapshot.hasError || !snapshot.hasData) {
          return const SizedBox.shrink(); // Don't show anything on error
        }

        final stats = snapshot.data!;
        final String points = (stats['points'] ?? 0).toString();
        final double balance = (stats['balance'] as num?)?.toDouble() ?? 0.0;

        return NeuCard(
          margin: const EdgeInsets.fromLTRB(16, 16, 16, 0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStat(
                icon: EvaIcons.star,
                label: 'Points',
                value: points,
                color: const Color(0xFFFFE66D),
              ),
              _buildStat(
                icon: Icons.account_balance_wallet_outlined,
                label: 'Balance',
                value: 'GHS ${balance.toStringAsFixed(2)}',
                color: const Color(0xFF4ECDC4),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStat({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Expanded(
      child: Column(
        children: [
          Icon(icon, color: color, size: 28),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          Text(label, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
        ],
      ),
    );
  }
}
