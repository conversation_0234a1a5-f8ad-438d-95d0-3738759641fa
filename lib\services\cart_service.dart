import 'dart:convert';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/places_service.dart'; // For WickerHttpClient

class CartService {
  final WickerHttpClient _client = WickerHttpClient();
  final ConfigService _config = ConfigService.instance;

  /// Retrieves the user's cart with populated product details.
  Future<Map<String, dynamic>> getCart() async {
    final baseUrl = await _config.getBaseUrl();
    try {
      final response = await _client.get(Uri.parse('$baseUrl/api/cart/'));

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to load cart');
      }
    } catch (e) {
      print('Get cart error: $e');
      rethrow;
    }
  }

  /// Adds a product to the user's cart.
  Future<void> addToCart(String productId, {int quantity = 1}) async {
    final baseUrl = await _config.getBaseUrl();
    try {
      final response = await _client.post(
        Uri.parse('$baseUrl/api/cart/add'),
        body: jsonEncode({'product_id': productId, 'quantity': quantity}),
      );

      if (response.statusCode != 200) {
        final body = jsonDecode(response.body);
        throw Exception(body['msg'] ?? 'Failed to add item to cart');
      }
    } catch (e) {
      rethrow;
    }
  }

  /// Updates the quantity of an item in the cart.
  Future<void> updateItemQuantity(String productId, int quantity) async {
    final baseUrl = await _config.getBaseUrl();
    try {
      final response = await _client.put(
        Uri.parse('$baseUrl/api/cart/update/$productId'),
        body: jsonEncode({'quantity': quantity}),
      );
      if (response.statusCode != 200) {
        final body = jsonDecode(response.body);
        throw Exception(body['msg'] ?? 'Failed to update cart');
      }
    } catch (e) {
      rethrow;
    }
  }

  /// Removes a product completely from the user's cart.
  Future<void> removeFromCart(String productId) async {
    final baseUrl = await _config.getBaseUrl();
    try {
      final response = await _client.delete(
        Uri.parse('$baseUrl/api/cart/remove/$productId'),
      );
      if (response.statusCode != 200) {
        final body = jsonDecode(response.body);
        throw Exception(body['msg'] ?? 'Failed to remove item');
      }
    } catch (e) {
      rethrow;
    }
  }

  // Add this method to your CartService class

  Future<void> clearCart() async {
    try {
      final baseUrl = await _config.getBaseUrl();
      final response = await _client.delete(
        Uri.parse('$baseUrl/api/cart/clear'),
      );

      if (response.statusCode != 200) {
        throw Exception('Failed to clear cart: ${response.body}');
      }
    } catch (e) {
      throw Exception('Error clearing cart: $e');
    }
  }
}
