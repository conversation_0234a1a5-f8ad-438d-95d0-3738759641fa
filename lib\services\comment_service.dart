import 'dart:convert';
import 'package:wicker/services/places_service.dart';
import 'package:wicker/services/config_service.dart';

class CommentService {
  final WickerHttpClient _client = WickerHttpClient();
  final ConfigService _config = ConfigService.instance;

  // REFACTORED: Now requires parentId and parentType
  Future<List<Map<String, dynamic>>> getComments({
    required String parentId,
    required String parentType, // 'post' or 'business'
    String? searchQuery,
    String? sentiment,
  }) async {
    try {
      final baseUrl = await _config.getBaseUrl();
      final uri = Uri.parse('$baseUrl/api/comments/$parentId').replace(
        queryParameters: {
          'type': parentType, // Pass the type to the backend
          if (searchQuery != null && searchQuery.isNotEmpty)
            'search': searchQuery,
          if (sentiment != null && sentiment != 'all') 'sentiment': sentiment,
        },
      );
      final response = await _client.get(uri);
      if (response.statusCode == 200) {
        List<dynamic> data = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(data);
      } else {
        throw Exception('Failed to load comments');
      }
    } catch (e) {
      rethrow;
    }
  }

  // --- NEW: Method to like a comment ---
  Future<void> likeComment(String commentId) async {
    try {
      final baseUrl = await _config.getBaseUrl();
      final response = await _client.post(
        Uri.parse('$baseUrl/api/comments/$commentId/like'),
      );
      if (response.statusCode != 200) {
        throw Exception('Failed to like comment');
      }
    } catch (e) {
      rethrow;
    }
  }

  // --- NEW: Method to dislike a comment ---
  Future<void> dislikeComment(String commentId) async {
    try {
      final baseUrl = await _config.getBaseUrl();
      final response = await _client.post(
        Uri.parse('$baseUrl/api/comments/$commentId/dislike'),
      );
      if (response.statusCode != 200) {
        throw Exception('Failed to dislike comment');
      }
    } catch (e) {
      rethrow;
    }
  }

  // --- REFACTORED: Now accepts a generic parentId and parentType ---
  Future<void> postComment({
    required String parentId,
    required String parentType,
    String? replyToId, // For threaded replies
    required String commentText,
  }) async {
    try {
      final baseUrl = await _config.getBaseUrl();
      final body = {
        'comment_text': commentText,
        'parent_id': replyToId,
        '${parentType}_id': parentId,
      };

      final response = await _client.post(
        Uri.parse('$baseUrl/api/comments/create'),
        body: jsonEncode(body),
      );
      if (response.statusCode != 201) {
        throw Exception('Failed to post comment');
      }
    } catch (e) {
      rethrow;
    }
  }
}
