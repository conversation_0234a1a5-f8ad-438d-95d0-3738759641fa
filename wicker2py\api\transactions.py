from werkzeug.utils import secure_filename
import os
import uuid 
import requests
import json
import hashlib
import hmac
import pymongo # Make sure to import pymongo at the top of the file
from flask import request, jsonify, Blueprint, abort, Response
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId, json_util
import datetime
import pymongo # Make sure to import pymongo at the top of the file


transactions_bp = Blueprint('transactions_bp', __name__)

PAYSTACK_SECRET_KEY = os.getenv('PAYSTACK_SECRET_KEY', 'YOUR_PAYSTACK_SECRET_KEY')
PAYSTACK_API_URL = "https://api.paystack.co"
BACKEND_BASE_URL = "https://d5778b57eab7.ngrok-free.app"


@transactions_bp.route('/initiate', methods=['POST'])
@jwt_required()
def initiate_transaction():
    """
    Initiates a new transaction for a SINGLE product.
    """
    db = transactions_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    data = request.get_json()
    product_id = data.get('product_id')

    if not product_id:
        return jsonify({"msg": "Product ID is required"}), 400

    product = db.products.find_one({"_id": ObjectId(product_id)})
    if not product:
        return jsonify({"msg": "Product not found"}), 404

    business = db.businesses.find_one({"_id": product['business_id']})
    if not business:
        return jsonify({"msg": "Business associated with product not found"}), 404
        
    user = db.users.find_one({"_id": current_user_id})
    if not user:
        return jsonify({"msg": "Buyer not found"}), 404

    new_order = {
        "buyer_id": current_user_id,
        "seller_id": business['owner_id'],
        "items": [{"product_id": ObjectId(product_id), "quantity": 1}],
        "total_amount": product['price'],
        "status": "pending",
        "paystack_reference": None,
        "created_at": datetime.datetime.now(datetime.timezone.utc),
        "updated_at": datetime.datetime.now(datetime.timezone.utc)
    }
    
    order_result = db.orders.insert_one(new_order)
    order_id = order_result.inserted_id

    headers = {"Authorization": f"Bearer {PAYSTACK_SECRET_KEY}", "Content-Type": "application/json"}
    amount_in_kobo = int(product['price'] * 100)

    payload = {
        "email": user['email'],
        "amount": amount_in_kobo,
        "callback_url": f"{BACKEND_BASE_URL}/api/transactions/webhook",
        "metadata": { "order_id": str(order_id), "source": "single_purchase" }
    }

    try:
        response = requests.post(f"{PAYSTACK_API_URL}/transaction/initialize", headers=headers, data=json.dumps(payload))
        response.raise_for_status()
        paystack_data = response.json()

        if paystack_data['status'] is True:
            db.orders.update_one({"_id": order_id}, {"$set": {"paystack_reference": paystack_data['data']['reference']}})
            return jsonify({"msg": "Transaction initiated", "authorization_url": paystack_data['data']['authorization_url']}), 200
        else:
            return jsonify({"msg": "Failed to initialize payment with Paystack"}), 500

    except requests.exceptions.RequestException as e:
        return jsonify({"msg": "Could not connect to payment gateway", "error": str(e)}), 503
    except Exception as e:
        return jsonify({"msg": "An unexpected error occurred", "error": str(e)}), 500

@transactions_bp.route('/initiate-cart-checkout', methods=['POST'])
@jwt_required()
def initiate_cart_checkout():
    """
    Initiates a transaction for all items in the user's cart from a SINGLE seller.
    """
    db = transactions_bp.db
    current_user_id = ObjectId(get_jwt_identity())

    cart = db.carts.find_one({'user_id': current_user_id})
    if not cart or not cart.get('items'):
        return jsonify({"msg": "Your cart is empty"}), 400

    total_amount = 0
    order_items = []
    seller_id = None
    
    # --- THE FIX: Validate all items are from the same seller ---
    first_product = db.products.find_one({"_id": ObjectId(cart['items'][0]['product_id'])})
    if not first_product:
         return jsonify({"msg": "Cart contains an invalid item"}), 400
    
    first_business_id = first_product['business_id']
    business = db.businesses.find_one({"_id": first_business_id})
    if not business:
        return jsonify({"msg": "Could not find business for item in cart"}), 404
    seller_id = business['owner_id']
    # --- End of FIX ---

    for item in cart['items']:
        product = db.products.find_one({"_id": ObjectId(item['product_id'])})
        if product:
            # --- THE FIX: Ensure all products belong to the same business ---
            if product['business_id'] != first_business_id:
                return jsonify({"msg": "Checkout is limited to one shop per order."}), 400
            # --- End of FIX ---

            total_amount += product['price'] * item['quantity']
            order_items.append({
                "product_id": item['product_id'],
                "quantity": item['quantity'],
                "price_at_purchase": product['price']
            })

    if total_amount == 0:
        return jsonify({"msg": "No valid items in cart to checkout"}), 400

    user = db.users.find_one({"_id": current_user_id})

    new_order = {
        "buyer_id": current_user_id,
        "seller_id": seller_id, # --- THE FIX: Add the seller_id ---
        "items": order_items,
        "total_amount": total_amount,
        "status": "pending",
        "paystack_reference": None,
        "created_at": datetime.datetime.now(datetime.timezone.utc),
        "updated_at": datetime.datetime.now(datetime.timezone.utc)
    }
    order_result = db.orders.insert_one(new_order)
    order_id = order_result.inserted_id

    headers = {"Authorization": f"Bearer {PAYSTACK_SECRET_KEY}", "Content-Type": "application/json"}
    amount_in_kobo = int(total_amount * 100)
    payload = {
        "email": user['email'],
        "amount": amount_in_kobo,
        "callback_url": f"{BACKEND_BASE_URL}/api/transactions/webhook",
        "metadata": { "order_id": str(order_id), "source": "cart_checkout" }
    }

    try:
        response = requests.post(f"{PAYSTACK_API_URL}/transaction/initialize", headers=headers, data=json.dumps(payload))
        response.raise_for_status()
        paystack_data = response.json()

        if paystack_data.get('status'):
            db.orders.update_one({"_id": order_id}, {"$set": {"paystack_reference": paystack_data['data']['reference']}})
            return jsonify({"authorization_url": paystack_data['data']['authorization_url']}), 200
        else:
            return jsonify({"msg": "Failed to initialize cart payment"}), 500
    except Exception as e:
        return jsonify({"msg": "An error occurred during cart checkout", "error": str(e)}), 500

@transactions_bp.route('/my-orders', methods=['GET'])
@jwt_required()
def get_my_orders():
    """
    Retrieves all orders placed by the current user, populating product details.
    Supports searching and sorting.
    """
    db = transactions_bp.db
    current_user_id = ObjectId(get_jwt_identity())

    # --- NEW: Get query parameters for search and sort ---
    search_query = request.args.get('search', None)
    sort_by = request.args.get('sort_by', 'created_at')
    sort_order_str = request.args.get('sort_order', 'desc')
    sort_order = pymongo.DESCENDING if sort_order_str == 'desc' else pymongo.ASCENDING

    # --- REFACTORED: Dynamic Pipeline Construction ---
    pipeline = [
        {'$match': {'buyer_id': current_user_id}},
        {'$unwind': '$items'},
        {
            '$lookup': {
                'from': 'products',
                'localField': 'items.product_id',
                'foreignField': '_id',
                'as': 'product_info'
            }
        },
        {'$unwind': '$product_info'},
    ]

    # Add search stage if a query is provided
    if search_query:
        pipeline.append({
            '$match': {
                'product_info.product_name': {'$regex': search_query, '$options': 'i'}
            }
        })

    # Add fields and group back
    pipeline.extend([
        {
            '$addFields': {
                'items.product_details': '$product_info'
            }
        },
        {
            '$group': {
                '_id': '$_id',
                'buyer_id': {'$first': '$buyer_id'},
                'seller_id': {'$first': '$seller_id'},
                'total_amount': {'$first': '$total_amount'},
                'status': {'$first': '$status'},
                'paystack_reference': {'$first': '$paystack_reference'},
                'created_at': {'$first': '$created_at'},
                'updated_at': {'$first': '$updated_at'},
                'items': {'$push': '$items'}
            }
        }
    ])

    # Add sorting stage
    valid_sort_fields = ['created_at', 'total_amount', 'status']
    if sort_by not in valid_sort_fields:
        sort_by = 'created_at'  # Default to created_at if invalid
    
    pipeline.append({'$sort': {sort_by: sort_order}})

    try:
        orders = list(db.orders.aggregate(pipeline))
        return Response(json_util.dumps(orders), mimetype='application/json'), 200
    except Exception as e:
        print(f"Error fetching orders: {e}")
        return jsonify({"msg": "An error occurred fetching orders", "error": str(e)}), 500


@transactions_bp.route('/webhook', methods=['GET', 'POST'])
def paystack_webhook():
    if request.method == 'POST':
        db = transactions_bp.db
        
        signature = request.headers.get('x-paystack-signature')
        if not signature:
            abort(400)

        hash = hmac.new(PAYSTACK_SECRET_KEY.encode('utf-8'), request.data, hashlib.sha512).hexdigest()
        if hash != signature:
            abort(401)

        event_data = request.get_json()
        event_type = event_data.get('event')

        if event_type == 'charge.success':
            metadata = event_data['data']['metadata']
            order_id = metadata.get('order_id')
            source = metadata.get('source')
            
            if order_id:
                order = db.orders.find_one_and_update(
                    {"_id": ObjectId(order_id)},
                    {"$set": {"status": "paid", "updated_at": datetime.datetime.now(datetime.timezone.utc)}}
                )
                print(f"Order {order_id} status updated to 'paid'.")

                if source == 'cart_checkout' and order:
                    buyer_id = order.get('buyer_id')
                    result = db.carts.delete_one({"user_id": ObjectId(buyer_id)})
                    print(f"Cleared cart for user {buyer_id}. Deleted count: {result.deleted_count}")

        return jsonify({"status": "success"}), 200
    else: 
        trxref = request.args.get('trxref')
        reference = request.args.get('reference')
        
        print(f"Payment redirect received - trxref: {trxref}, reference: {reference}")
        
        return '''
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Payment Successful</title>
            <style>
                body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background-color: #f0f0f0; }
                .success { background-color: #4CAF50; color: white; padding: 20px; border-radius: 10px; margin: 20px auto; max-width: 400px; }
            </style>
        </head>
        <body>
            <div class="success">
                <h1>✅ Payment Successful!</h1>
                <p>Your payment has been processed successfully.</p>
                <p>Redirecting back to the app...</p>
            </div>
            <script>
                console.log('Payment success page loaded');
                function notifyFlutter() {
                    if (window.PaymentChannel) {
                        console.log('Sending payment_success via PaymentChannel');
                        window.PaymentChannel.postMessage('payment_success');
                    }
                    if (window.flutter_inappwebview) {
                        console.log('Sending via flutter_inappwebview');
                        window.flutter_inappwebview.callHandler('payment_success');
                    }
                }
                notifyFlutter();
                setTimeout(notifyFlutter, 500);
                setTimeout(notifyFlutter, 1000);
                setTimeout(function() {
                    console.log('Fallback: attempting to close window');
                    if (window.close) { window.close(); }
                }, 3000);
            </script>
        </body>
        </html>
        '''
    

@transactions_bp.route('/order/<order_id>/mark-delivered', methods=['POST'])
@jwt_required()
def mark_as_delivered(order_id):
    """
    SELLER-ONLY endpoint. Marks an order as delivered and uploads proof.
    """
    db = transactions_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    
    order = db.orders.find_one({
        "_id": ObjectId(order_id),
        "seller_id": current_user_id
    })
    if not order:
        return jsonify({"msg": "Order not found or you are not the seller"}), 404
        
    if order['status'] != 'paid':
        return jsonify({"msg": f"Order is not in 'paid' status. Current status: {order['status']}"}), 400

    proof_image_path = None
    if 'proof_image' in request.files:
        image = request.files['proof_image']
        if image.filename != '':
            filename = secure_filename(image.filename)
            unique_filename = f"{uuid.uuid4()}_{filename}"
            upload_folder = 'uploads/proof'
            if not os.path.exists(upload_folder):
                os.makedirs(upload_folder)
            
            proof_image_path = os.path.join(upload_folder, unique_filename)
            image.save(proof_image_path)

    db.orders.update_one(
        {"_id": ObjectId(order_id)},
        {
            "$set": {
                "status": "delivered",
                "proof_of_delivery_url": proof_image_path,
                "updated_at": datetime.datetime.now(datetime.timezone.utc)
            }
        }
    )
    return jsonify({"msg": "Order marked as delivered"}), 200



@transactions_bp.route('/order/<order_id>/confirm-receipt', methods=['POST'])
@jwt_required()
def confirm_receipt(order_id):
    """
    BUYER-ONLY endpoint. Confirms receipt, completes the transaction,
    and initiates payout to the seller.
    """
    db = transactions_bp.db
    current_user_id = ObjectId(get_jwt_identity())

    order = db.orders.find_one({
        "_id": ObjectId(order_id),
        "buyer_id": current_user_id
    })
    if not order:
        return jsonify({"msg": "Order not found or you are not the buyer"}), 404

    if order['status'] != 'delivered':
        return jsonify({"msg": "Order has not been marked as delivered by the seller yet."}), 400

    seller_id = order['seller_id']
    total_amount = order['total_amount']
    seller_user = db.users.find_one({"_id": seller_id})
    recipient_code = seller_user.get('paystack_recipient_code')
    payout_status = "manual_payout_required"
    
    if recipient_code:
        headers = {"Authorization": f"Bearer {PAYSTACK_SECRET_KEY}", "Content-Type": "application/json"}
        # Paystack takes a small fee, so we might not transfer the full amount.
        # For simplicity, we'll transfer the full amount minus a placeholder 1% fee.
        # In a real app, you would define your commission structure.
        amount_to_transfer_kobo = int((total_amount * 0.99) * 100)

        payload = {
            "source": "balance",
            "amount": amount_to_transfer_kobo,
            "recipient": recipient_code,
            "reason": f"Payout for Wicker Order #{str(order_id)[:8]}"
        }
        try:
            response = requests.post(f"{PAYSTACK_API_URL}/transfer", headers=headers, data=json.dumps(payload))
            response.raise_for_status()
            transfer_data = response.json()
            if transfer_data.get('status'):
                transfer_code = transfer_data['data']['transfer_code']
                db.orders.update_one({"_id": ObjectId(order_id)}, {"$set": {"paystack_transfer_code": transfer_code}})
                payout_status = "payout_initiated"
        except Exception as e:
            print(f"Paystack transfer initiation failed for order {order_id}: {e}")
    
    db.orders.update_one(
        {"_id": ObjectId(order_id)},
        {"$set": {
            "status": "completed",
            "payout_status": payout_status,
            "updated_at": datetime.datetime.now(datetime.timezone.utc)
        }}
    )
    db.users.update_one(
        {"_id": seller_id},
        {"$inc": {"balance": total_amount}}
    )

    msg = "Receipt confirmed. Seller's balance has been updated."
    if payout_status == "payout_initiated":
        msg = "Receipt confirmed. Payout to seller has been initiated."
    
    return jsonify({"msg": msg}), 200





@transactions_bp.route('/my-sales', methods=['GET'])
@jwt_required()
def get_my_sales():
    """
    Retrieves all sales orders for the current user's business.
    Supports searching, sorting, and calculates sales totals per status.
    """
    db = transactions_bp.db
    current_user_id = ObjectId(get_jwt_identity())

    # --- NEW: Get query parameters for search and filter ---
    search_query = request.args.get('search', None)
    status_filter = request.args.get('status', None)

    # Find the business owned by the current user
    business = db.businesses.find_one({'owner_id': current_user_id})
    if not business:
        return jsonify({"msg": "No business found for this user."}), 404

    # --- REFACTORED: Dynamic Pipeline Construction ---
    base_pipeline = [
        {'$match': {'seller_id': current_user_id}},
        {'$unwind': '$items'},
        {
            '$lookup': {
                'from': 'products',
                'localField': 'items.product_id',
                'foreignField': '_id',
                'as': 'product_info'
            }
        },
        {'$unwind': '$product_info'}
    ]

    # Add search stage if a query is provided
    if search_query:
        base_pipeline.append({
            '$match': {
                'product_info.product_name': {'$regex': search_query, '$options': 'i'}
            }
        })
    
    # Add status filter stage
    if status_filter and status_filter != 'all':
        base_pipeline.append({
            '$match': {'status': status_filter}
        })
    
    # --- NEW: Use $facet to get both results and summary stats in one query ---
    facet_pipeline = [
        {
            '$facet': {
                'sales_data': [
                    {'$group': {
                        '_id': '$_id',
                        'buyer_id': {'$first': '$buyer_id'},
                        'total_amount': {'$first': '$total_amount'},
                        'status': {'$first': '$status'},
                        'created_at': {'$first': '$created_at'},
                        'items': {'$push': '$items.product_info'}
                    }},
                    {'$sort': {'created_at': -1}}
                ],
                'summary_stats': [
                    {'$group': {
                        '_id': '$status',
                        'total_sales': {'$sum': '$total_amount'},
                        'count': {'$sum': 1}
                    }}
                ]
            }
        }
    ]

    try:
        full_pipeline = base_pipeline + facet_pipeline
        result = list(db.orders.aggregate(full_pipeline))
        
        # The result of a facet is an array containing one document
        if not result or not result[0]['sales_data']:
             return jsonify({"sales_data": [], "summary_stats": []}), 200

        return Response(json_util.dumps(result[0]), mimetype='application/json'), 200
    except Exception as e:
        print(f"Error fetching sales: {e}")
        return jsonify({"msg": "An error occurred fetching sales", "error": str(e)}), 500